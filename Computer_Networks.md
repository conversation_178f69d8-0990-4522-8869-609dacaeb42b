# Computer Networks - Complete Interview Guide

## Table of Contents

1. [Network Fundamentals](#fundamentals)
2. [OSI Model - 7 Layer Architecture](#osi-model)
3. [TCP/IP Model - Internet Protocol Suite](#tcpip-model)
4. [IP Addressing and Subnetting](#ip-addressing)
5. [TCP vs UDP - Transport Protocols](#tcp-udp)
6. [HTTP/HTTPS - Web Communication](#http-https)
7. [DNS - Domain Name System](#dns)
8. [Ports and Sockets](#ports-sockets)
9. [Routing and Switching](#routing-switching)
10. [Network Security](#security)
11. [Load Balancing](#load-balancing)
12. [CDN - Content Delivery Networks](#cdns)
13. [WebSockets and Real-time Communication](#websockets)
14. [Network Performance and Optimization](#performance)
15. [Common Interview Questions](#interview-questions)

---

## 1. Network Fundamentals {#fundamentals}

### What is a Computer Network?

A computer network is a collection of interconnected devices that can communicate and share resources. Networks enable data transmission between devices regardless of their physical location.

### Core Components

**Node**: Any device connected to a network (computer, server, router, switch, printer)

**Link**: Physical or wireless connection between nodes (cables, WiFi, fiber optic)

**Protocol**: Set of rules governing communication between devices

**Data Packet**: Unit of data transmitted across networks, containing both data and control information

### Basic Network Communication Flow

```
Source Device → Network Infrastructure → Destination Device
      ↓                    ↓                      ↓
   [Sender]           [Transmission]          [Receiver]
```

### Network Types by Scope

**PAN (Personal Area Network)**

- Range: 1-10 meters
- Examples: Bluetooth, USB connections
- Use: Connecting personal devices

**LAN (Local Area Network)**

- Range: Single building or campus
- Examples: Office networks, home WiFi
- Characteristics: High speed, low latency, single administrative domain

**MAN (Metropolitan Area Network)**

- Range: City or metropolitan area
- Examples: City-wide WiFi, cable TV networks
- Characteristics: Covers larger area than LAN, connects multiple LANs

**WAN (Wide Area Network)**

- Range: Countries, continents
- Examples: Internet, corporate networks spanning multiple cities
- Characteristics: Lower speed than LAN, higher latency, multiple administrative domains

### Network Topologies

**Bus Topology**

```
Device1 ─── Device2 ─── Device3 ─── Device4
```

- All devices connected to single cable
- Data travels in both directions
- Single point of failure

**Star Topology**

```
    Device1
       │
Device4 ─ Hub ─ Device2
       │
    Device3
```

- All devices connect to central hub/switch
- Most common in modern networks
- Hub failure affects entire network

**Ring Topology**

```
Device1 ─── Device2
   │           │
Device4 ─── Device3
```

- Devices connected in circular fashion
- Data travels in one direction
- Break in ring affects entire network

**Mesh Topology**

```
Device1 ─── Device2
   │    \  /    │
   │     ×      │
   │    / \     │
Device4 ─── Device3
```

- Every device connected to every other device
- Highly redundant and reliable
- Expensive to implement

### Network Hardware Components

**Hub**: Basic device that repeats data to all connected devices (largely obsolete)

**Switch**: Intelligent device that learns MAC addresses and forwards data only to intended recipient

**Router**: Device that connects different networks and makes routing decisions based on IP addresses

**Gateway**: Device that connects networks using different protocols

**Modem**: Device that converts digital signals to analog (and vice versa) for transmission over telephone lines or cable

### Data Flow in Networks

```
Application Data → Packetization → Transmission → Reassembly → Application Data
       ↓                ↓              ↓             ↓              ↓
   [Original]      [Add Headers]   [Send Packets]  [Remove Headers] [Reconstructed]
```

---

## 2. OSI Model - 7 Layer Architecture {#osi-model}

### What is the OSI Model?

The OSI (Open Systems Interconnection) model is a conceptual framework that standardizes network communication functions into seven distinct layers. Each layer has specific responsibilities and communicates only with adjacent layers.

### Why OSI Model Matters

- **Standardization**: Provides common language for network professionals
- **Troubleshooting**: Helps isolate problems to specific layers
- **Design**: Guides network architecture and protocol development
- **Interoperability**: Ensures different vendors' equipment can work together

### The 7 Layers Overview

```
Layer 7: Application  ← User Interface
Layer 6: Presentation ← Data Formatting
Layer 5: Session      ← Connection Management
Layer 4: Transport    ← End-to-End Delivery
Layer 3: Network      ← Routing
Layer 2: Data Link    ← Local Delivery
Layer 1: Physical     ← Electrical Signals
```

### Layer 1: Physical Layer

**Purpose**: Transmits raw bits over physical medium

**Responsibilities**:

- Electrical signal transmission
- Cable specifications
- Connector types
- Bit synchronization

**Examples**: Ethernet cables, fiber optic cables, radio frequencies, voltage levels

**Data Unit**: Bits (1s and 0s)

**Devices**: Hubs, repeaters, cables, connectors

### Layer 2: Data Link Layer

**Purpose**: Provides reliable data transfer between adjacent nodes

**Responsibilities**:

- Frame formatting
- Error detection and correction
- Flow control
- MAC address handling

**Examples**: Ethernet, WiFi (802.11), PPP

**Data Unit**: Frames

**Devices**: Switches, bridges, network interface cards

**Frame Structure**:

```
[Preamble][Destination MAC][Source MAC][Type][Data][CRC]
```

### Layer 3: Network Layer

**Purpose**: Routes packets between different networks

**Responsibilities**:

- Logical addressing (IP addresses)
- Path determination
- Packet forwarding
- Fragmentation and reassembly

**Examples**: IP (IPv4/IPv6), ICMP, OSPF, BGP

**Data Unit**: Packets

**Devices**: Routers, Layer 3 switches

**Packet Structure**:

```
[IP Header][Data from Transport Layer]
```

### Layer 4: Transport Layer

**Purpose**: Provides end-to-end communication between applications

**Responsibilities**:

- Segmentation and reassembly
- Error recovery
- Flow control
- Port addressing

**Examples**: TCP, UDP

**Data Unit**: Segments (TCP) or Datagrams (UDP)

**Devices**: Gateways, firewalls

### Layer 5: Session Layer

**Purpose**: Manages sessions between applications

**Responsibilities**:

- Session establishment, maintenance, and termination
- Session checkpointing and recovery
- Dialog control (full-duplex or half-duplex)

**Examples**: NetBIOS, RPC, SQL sessions

**Data Unit**: Data

**Key Concepts**:

- **Session**: Logical connection between applications
- **Checkpointing**: Saving session state for recovery
- **Dialog Control**: Managing turn-taking in communication

### Layer 6: Presentation Layer

**Purpose**: Data formatting, encryption, and compression

**Responsibilities**:

- Data encryption and decryption
- Data compression and decompression
- Character encoding (ASCII, Unicode)
- Data format conversion

**Examples**: SSL/TLS, JPEG, MPEG, ASCII

**Data Unit**: Data

**Functions**:

```
Raw Data → Encryption → Compression → Formatted Data
```

### Layer 7: Application Layer

**Purpose**: Provides network services to applications

**Responsibilities**:

- Network process to application
- User interface
- Application services

**Examples**: HTTP, HTTPS, FTP, SMTP, DNS, DHCP

**Data Unit**: Data

**Common Protocols**:

- **HTTP/HTTPS**: Web browsing
- **FTP**: File transfer
- **SMTP**: Email sending
- **POP3/IMAP**: Email receiving
- **DNS**: Domain name resolution

### Data Flow Through OSI Layers

**Sending Data (Top to Bottom)**:

```
Layer 7: Application Data
         ↓ (Add Application Header)
Layer 6: Encrypted/Compressed Data
         ↓ (Add Session Header)
Layer 5: Session Data
         ↓ (Add Transport Header)
Layer 4: Segments/Datagrams
         ↓ (Add Network Header)
Layer 3: Packets
         ↓ (Add Data Link Header)
Layer 2: Frames
         ↓ (Convert to Signals)
Layer 1: Bits
```

**Receiving Data (Bottom to Top)**:

```
Layer 1: Bits
         ↓ (Convert to Frames)
Layer 2: Frames
         ↓ (Remove Data Link Header)
Layer 3: Packets
         ↓ (Remove Network Header)
Layer 4: Segments/Datagrams
         ↓ (Remove Transport Header)
Layer 5: Session Data
         ↓ (Remove Session Header)
Layer 6: Encrypted/Compressed Data
         ↓ (Remove Application Header)
Layer 7: Application Data
```

---

## 3. TCP/IP Model - Internet Protocol Suite {#tcpip-model}

### What is TCP/IP Model?

TCP/IP (Transmission Control Protocol/Internet Protocol) is the practical model used by the Internet. It has 4 layers compared to OSI's 7 layers, making it simpler and more implementation-focused.

### TCP/IP vs OSI Comparison

```
OSI Model                    TCP/IP Model
---------                    ------------
Application    ┐
Presentation   ├─────────→   Application
Session        ┘
Transport      ─────────→    Transport
Network        ─────────→    Internet
Data Link      ┐
Physical       ┘─────────→   Network Access
```

### TCP/IP Layer 1: Network Access Layer

**Purpose**: Combines OSI Physical and Data Link layers

**Responsibilities**:

- Physical transmission of data
- Local network addressing (MAC addresses)
- Frame formatting
- Error detection on local network

**Examples**: Ethernet, WiFi, Token Ring

**Key Concepts**:

- **MAC Address**: 48-bit hardware address (e.g., 00:1B:44:11:3A:B7)
- **Frame**: Data unit at this layer
- **CSMA/CD**: Collision detection in Ethernet

### TCP/IP Layer 2: Internet Layer

**Purpose**: Equivalent to OSI Network layer

**Responsibilities**:

- Logical addressing (IP addresses)
- Routing between networks
- Packet fragmentation and reassembly

**Main Protocol**: IP (Internet Protocol)

**Supporting Protocols**:

- **ICMP**: Internet Control Message Protocol (ping, traceroute)
- **ARP**: Address Resolution Protocol (maps IP to MAC)
- **RARP**: Reverse ARP (maps MAC to IP)

### TCP/IP Layer 3: Transport Layer

**Purpose**: Same as OSI Transport layer

**Main Protocols**:

- **TCP**: Reliable, connection-oriented
- **UDP**: Unreliable, connectionless

**Responsibilities**:

- End-to-end communication
- Port addressing
- Error recovery (TCP only)
- Flow control (TCP only)

### TCP/IP Layer 4: Application Layer

**Purpose**: Combines OSI Session, Presentation, and Application layers

**Common Protocols**:

- **HTTP/HTTPS**: Web communication
- **FTP**: File transfer
- **SMTP**: Email sending
- **DNS**: Domain name resolution
- **DHCP**: Dynamic IP assignment
- **SSH**: Secure shell access

---

## 4. IP Addressing and Subnetting {#ip-addressing}

### IPv4 Addressing

**IPv4 Address Structure**: 32-bit address divided into 4 octets

- Format: X.X.X.X (where X is 0-255)
- Example: *************

**IPv4 Address Classes**:

**Class A**:

- Range: ******* to ***************
- Default Subnet Mask: ********* (/8)
- Network Bits: 8, Host Bits: 24
- Max Networks: 126, Max Hosts: 16,777,214

**Class B**:

- Range: ********* to ***************
- Default Subnet Mask: *********** (/16)
- Network Bits: 16, Host Bits: 16
- Max Networks: 16,384, Max Hosts: 65,534

**Class C**:

- Range: ********* to ***************
- Default Subnet Mask: ************* (/24)
- Network Bits: 24, Host Bits: 8
- Max Networks: 2,097,152, Max Hosts: 254

### Private IP Address Ranges

**RFC 1918 Private Addresses** (not routable on Internet):

- **Class A Private**: 10.0.0.0 to ************** (/8)
- **Class B Private**: ********** to ************** (/12)
- **Class C Private**: *********** to *************** (/16)

### Special IP Addresses

- **127.0.0.1**: Loopback address (localhost)
- **0.0.0.0**: Default route or "any" address
- *******************: Limited broadcast address
- **169.254.x.x**: APIPA (Automatic Private IP Addressing)

### Subnet Masks and CIDR Notation

**Subnet Mask**: Determines network and host portions of IP address

**CIDR (Classless Inter-Domain Routing)**: Modern addressing method

Examples:

- ***********/24 = *********** with subnet mask *************
- 10.0.0.0/8 = 10.0.0.0 with subnet mask *********

### Subnetting Fundamentals

**Purpose of Subnetting**:

- Divide large networks into smaller, manageable segments
- Improve network performance and security
- Efficient IP address utilization

**Subnetting Process**:

```
Original Network: ***********/24 (256 addresses)
                     ↓
Subnet 1: ***********/26   (64 addresses: .0-.63)
Subnet 2: ************/26  (64 addresses: .64-.127)
Subnet 3: *************/26 (64 addresses: .128-.191)
Subnet 4: *************/26 (64 addresses: .192-.255)
```

**Subnet Calculation Example**:

Network: ***********/26

- Subnet Mask: ***************
- Network Address: ***********
- Broadcast Address: ************
- Usable Host Range: *********** to ************
- Total Hosts: 62 (64 - 2 for network and broadcast)

### IPv6 Addressing

**IPv6 Address Structure**: 128-bit address

- Format: 8 groups of 4 hexadecimal digits
- Example: 2001:0db8:85a3:0000:0000:8a2e:0370:7334
- Compressed: 2001:db8:85a3::8a2e:370:7334

**IPv6 Address Types**:

- **Unicast**: One-to-one communication
- **Multicast**: One-to-many communication
- **Anycast**: One-to-nearest communication

**IPv6 Benefits**:

- Larger address space (340 undecillion addresses)
- Built-in security (IPSec)
- No need for NAT
- Auto-configuration capabilities

---

## 5. TCP vs UDP - Transport Protocols {#tcp-udp}

### TCP (Transmission Control Protocol)

**Characteristics**:

- **Connection-oriented**: Establishes connection before data transfer
- **Reliable**: Guarantees data delivery and order
- **Error checking**: Detects and corrects errors
- **Flow control**: Manages data transmission rate
- **Congestion control**: Adjusts to network conditions

**TCP Header Structure**:

```
[Source Port][Dest Port][Sequence Number][Acknowledgment]
[Flags][Window Size][Checksum][Urgent Pointer][Options][Data]
```

**TCP Three-Way Handshake**:

```
Client                    Server
  │                         │
  ├─── SYN ────────────────→ │  (1. Request connection)
  │                         │
  │ ←──── SYN-ACK ──────────┤  (2. Acknowledge + Request)
  │                         │
  ├─── ACK ────────────────→ │  (3. Acknowledge)
  │                         │
  │ ←──── Data Transfer ────→ │  (Connection established)
```

**TCP Connection Termination**:

```
Client                    Server
  │                         │
  ├─── FIN ────────────────→ │  (1. Request close)
  │                         │
  │ ←──── ACK ──────────────┤  (2. Acknowledge)
  │                         │
  │ ←──── FIN ──────────────┤  (3. Server close request)
  │                         │
  ├─── ACK ────────────────→ │  (4. Final acknowledge)
```

### UDP (User Datagram Protocol)

**Characteristics**:

- **Connectionless**: No connection establishment
- **Unreliable**: No guarantee of delivery or order
- **Fast**: Minimal overhead
- **Simple**: Basic error checking only

**UDP Header Structure**:

```
[Source Port][Dest Port][Length][Checksum][Data]
```

### TCP vs UDP Comparison

| Feature            | TCP                 | UDP                       |
| ------------------ | ------------------- | ------------------------- |
| Connection         | Connection-oriented | Connectionless            |
| Reliability        | Reliable            | Unreliable                |
| Ordering           | Ordered delivery    | No ordering guarantee     |
| Speed              | Slower (overhead)   | Faster (minimal overhead) |
| Header Size        | 20+ bytes           | 8 bytes                   |
| Error Recovery     | Yes                 | No                        |
| Flow Control       | Yes                 | No                        |
| Congestion Control | Yes                 | No                        |

### When to Use TCP vs UDP

**Use TCP for**:

- Web browsing (HTTP/HTTPS)
- Email (SMTP, POP3, IMAP)
- File transfer (FTP)
- Remote access (SSH, Telnet)
- Database connections
- Any application requiring data integrity

**Use UDP for**:

- Live video/audio streaming
- Online gaming
- DNS queries
- DHCP
- Simple Network Management Protocol (SNMP)
- Applications where speed > reliability

---

## 6. HTTP/HTTPS - Web Communication {#http-https}

### HTTP (HyperText Transfer Protocol)

**Purpose**: Application layer protocol for web communication

**Characteristics**:

- **Stateless**: Each request is independent
- **Text-based**: Human-readable format
- **Request-Response**: Client sends request, server sends response
- **Port 80**: Default port for HTTP

**HTTP Request Structure**:

```
GET /api/users HTTP/1.1
Host: example.com
User-Agent: Mozilla/5.0
Accept: application/json
Content-Type: application/json

[Request Body - for POST/PUT requests]
```

**HTTP Response Structure**:

```
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 1234
Server: nginx/1.18.0

[Response Body]
```

### HTTP Methods

**GET**: Retrieve data from server

- Safe and idempotent
- No request body
- Data in URL parameters

**POST**: Send data to server

- Not idempotent
- Has request body
- Creates new resources

**PUT**: Update/replace resource

- Idempotent
- Has request body
- Updates entire resource

**PATCH**: Partial update

- Not necessarily idempotent
- Has request body
- Updates part of resource

**DELETE**: Remove resource

- Idempotent
- May have request body
- Removes resource

**HEAD**: Get headers only

- Like GET but no response body
- Used for checking resource existence

**OPTIONS**: Get allowed methods

- Used for CORS preflight requests

### HTTP Status Codes

**1xx - Informational**:

- 100 Continue
- 101 Switching Protocols

**2xx - Success**:

- 200 OK
- 201 Created
- 202 Accepted
- 204 No Content

**3xx - Redirection**:

- 301 Moved Permanently
- 302 Found (Temporary Redirect)
- 304 Not Modified
- 307 Temporary Redirect

**4xx - Client Error**:

- 400 Bad Request
- 401 Unauthorized
- 403 Forbidden
- 404 Not Found
- 405 Method Not Allowed
- 409 Conflict
- 422 Unprocessable Entity
- 429 Too Many Requests

**5xx - Server Error**:

- 500 Internal Server Error
- 501 Not Implemented
- 502 Bad Gateway
- 503 Service Unavailable
- 504 Gateway Timeout

### HTTPS (HTTP Secure)

**Purpose**: Secure version of HTTP using SSL/TLS encryption

**Characteristics**:

- **Encrypted**: Data encrypted in transit
- **Authentication**: Verifies server identity
- **Integrity**: Ensures data not tampered with
- **Port 443**: Default port for HTTPS

**HTTPS Handshake Process**:

```
Client                           Server
  │                                │
  ├─── Client Hello ─────────────→ │  (1. Supported ciphers)
  │                                │
  │ ←─── Server Hello ─────────────┤  (2. Chosen cipher + Certificate)
  │                                │
  ├─── Key Exchange ─────────────→ │  (3. Generate session keys)
  │                                │
  │ ←─── Finished ─────────────────┤  (4. Handshake complete)
  │                                │
  │ ←─── Encrypted Data ─────────→ │  (5. Secure communication)
```

### HTTP vs HTTPS

| Feature            | HTTP                 | HTTPS                                 |
| ------------------ | -------------------- | ------------------------------------- |
| Security           | None                 | SSL/TLS encryption                    |
| Port               | 80                   | 443                                   |
| Speed              | Faster               | Slightly slower (encryption overhead) |
| SEO                | Lower ranking        | Higher ranking                        |
| Browser indicators | "Not secure" warning | Lock icon                             |
| Data integrity     | No guarantee         | Cryptographically verified            |

---

## 7. DNS - Domain Name System {#dns}

### What is DNS?

DNS (Domain Name System) is a hierarchical distributed naming system that translates human-readable domain names into IP addresses.

**Purpose**: Convert domain names (google.com) to IP addresses (**************)

### DNS Hierarchy

```
Root (.)
    │
    ├── .com
    │    ├── google.com
    │    ├── facebook.com
    │    └── amazon.com
    │
    ├── .org
    │    ├── wikipedia.org
    │    └── mozilla.org
    │
    └── .net
         ├── cloudflare.net
         └── akamai.net
```

### DNS Record Types

**A Record**: Maps domain to IPv4 address

- Example: google.com → **************

**AAAA Record**: Maps domain to IPv6 address

- Example: google.com → 2607:f8b0:4004:c1b::65

**CNAME Record**: Maps domain to another domain

- Example: www.example.com → example.com

**MX Record**: Mail exchange servers

- Example: example.com → mail.example.com (priority 10)

**NS Record**: Name server records

- Example: example.com → ns1.example.com

**TXT Record**: Text information

- Example: Used for SPF, DKIM, domain verification

**PTR Record**: Reverse DNS lookup

- Example: ************** → google.com

### DNS Resolution Process

```
User types "google.com"
         ↓
1. Check browser cache
         ↓
2. Check OS cache
         ↓
3. Query local DNS resolver
         ↓
4. Query root name server
         ↓
5. Query .com name server
         ↓
6. Query google.com name server
         ↓
7. Return IP address
         ↓
8. Browser connects to IP
```

### DNS Caching

**Browser Cache**: Stores DNS records for short time (typically 5-30 minutes)

**OS Cache**: Operating system level DNS cache

**Router Cache**: Local router may cache DNS responses

**ISP Cache**: Internet Service Provider DNS cache

**TTL (Time To Live)**: Determines how long DNS records are cached

---

## 8. Ports and Sockets {#ports-sockets}

### What are Ports?

**Port**: 16-bit number (0-65535) that identifies specific services on a device

**Purpose**: Allows multiple services to run on same IP address

**Port Structure**:

```
IP Address : Port Number
************* : 80 (Web server)
************* : 22 (SSH server)
************* : 3306 (MySQL database)
```

### Port Categories

**Well-Known Ports (0-1023)**:

- Require administrative privileges
- Reserved for system services
- Examples: 80 (HTTP), 443 (HTTPS), 22 (SSH)

**Registered Ports (1024-49151)**:

- Assigned by IANA for specific services
- Examples: 3306 (MySQL), 5432 (PostgreSQL), 6379 (Redis)

**Dynamic/Private Ports (49152-65535)**:

- Used for client connections
- Automatically assigned by OS

### Common Port Numbers

| Port  | Service | Protocol |
| ----- | ------- | -------- |
| 20/21 | FTP     | TCP      |
| 22    | SSH     | TCP      |
| 23    | Telnet  | TCP      |
| 25    | SMTP    | TCP      |
| 53    | DNS     | UDP/TCP  |
| 67/68 | DHCP    | UDP      |
| 80    | HTTP    | TCP      |
| 110   | POP3    | TCP      |
| 143   | IMAP    | TCP      |
| 443   | HTTPS   | TCP      |
| 993   | IMAPS   | TCP      |
| 995   | POP3S   | TCP      |

### Sockets

**Socket**: Endpoint for network communication

**Socket = IP Address + Port Number + Protocol**

**Socket Types**:

**Stream Sockets (TCP)**:

- Reliable, ordered data delivery
- Connection-oriented
- Used for HTTP, FTP, SSH

**Datagram Sockets (UDP)**:

- Unreliable, unordered delivery
- Connectionless
- Used for DNS, DHCP, streaming

### Socket Communication Flow

```
Server                          Client
  │                               │
  ├─ socket() ──────────────────  ├─ socket()
  │                               │
  ├─ bind(port) ─────────────────  │
  │                               │
  ├─ listen() ───────────────────  │
  │                               │
  ├─ accept() ───────────────────  ├─ connect()
  │                               │
  │ ←──── Connection Established ──→ │
  │                               │
  │ ←──── Data Exchange ─────────→ │
  │                               │
  ├─ close() ────────────────────  ├─ close()
---

## 9. Routing and Switching {#routing-switching}

### Routing

**Purpose**: Process of determining the best path for data packets across networks

**Router Functions**:
- Path determination using routing tables
- Packet forwarding between networks
- Network address translation (NAT)
- Traffic filtering and security

**Routing Table Structure**:
```

Destination Network | Subnet Mask | Gateway | Interface | Metric
*********** | /24 | Local | eth0 | 1
10.0.0.0 | /8 | ********| eth1 | 5
0.0.0.0 | /0 | ISP | eth2 | 10

```

**Routing Protocols**:

**Static Routing**:
- Manually configured routes
- No automatic updates
- Suitable for small, stable networks

**Dynamic Routing**:
- Automatic route discovery and updates
- Adapts to network changes
- Uses routing protocols

**Interior Gateway Protocols (IGP)**:
- **RIP (Routing Information Protocol)**: Distance vector, hop count metric
- **OSPF (Open Shortest Path First)**: Link state, bandwidth-based metric
- **EIGRP (Enhanced Interior Gateway Routing Protocol)**: Cisco proprietary, hybrid

**Exterior Gateway Protocols (EGP)**:
- **BGP (Border Gateway Protocol)**: Used between autonomous systems on Internet

### Switching

**Purpose**: Forwards data frames within a local network using MAC addresses

**Switch Functions**:
- MAC address learning
- Frame forwarding and filtering
- Loop prevention (Spanning Tree Protocol)
- VLAN support

**Switch Operation**:
```

1. Receive frame on port
   ↓
2. Learn source MAC address
   ↓
3. Check destination MAC in table
   ↓
4. Forward to specific port (if known)
   OR Flood to all ports (if unknown)

```

**MAC Address Table**:
```

MAC Address | Port | VLAN | Age
AA:BB:CC:DD:EE:FF | 1 | 10 | 120
11:22:33:44:55:66 | 3 | 20 | 45

### VLAN (Virtual LAN)

**Purpose**: Logically separate networks on same physical infrastructure

**Benefits**:

- Improved security through network segmentation
- Reduced broadcast domains
- Flexible network management
- Cost savings

**VLAN Types**:

- **Data VLAN**: Carries user data
- **Voice VLAN**: Optimized for VoIP traffic
- **Management VLAN**: For network device management
- **Native VLAN**: Untagged traffic on trunk ports

---

## 10. Network Security {#security}

### Network Security Fundamentals

**CIA Triad**:

- **Confidentiality**: Data accessible only to authorized users
- **Integrity**: Data remains unaltered during transmission
- **Availability**: Network services remain accessible

### Common Network Threats

**Passive Attacks**:

- **Eavesdropping**: Intercepting network traffic
- **Traffic Analysis**: Analyzing communication patterns

**Active Attacks**:

- **Man-in-the-Middle**: Intercepting and modifying communications
- **Denial of Service (DoS)**: Overwhelming network resources
- **Distributed DoS (DDoS)**: DoS attack from multiple sources
- **Spoofing**: Impersonating legitimate network entities

### Network Security Devices

**Firewall**: Controls network traffic based on security rules

**Firewall Types**:

- **Packet Filtering**: Examines packet headers
- **Stateful Inspection**: Tracks connection states
- **Application Layer**: Deep packet inspection
- **Next-Generation**: Advanced threat detection

**Intrusion Detection System (IDS)**:

- Monitors network for suspicious activity
- Generates alerts for potential threats
- Types: Network-based (NIDS) and Host-based (HIDS)

**Intrusion Prevention System (IPS)**:

- Active threat blocking
- Real-time response to attacks
- Can drop malicious packets

### VPN (Virtual Private Network)

**Purpose**: Secure connection over public networks

**VPN Types**:

- **Site-to-Site**: Connects entire networks
- **Remote Access**: Individual user connections
- **Client-to-Site**: Remote users to corporate network

**VPN Protocols**:

- **IPSec**: Network layer security
- **SSL/TLS**: Application layer security
- **PPTP**: Point-to-Point Tunneling (legacy)
- **L2TP**: Layer 2 Tunneling Protocol

---

## 11. Load Balancing {#load-balancing}

### What is Load Balancing?

**Purpose**: Distribute incoming network traffic across multiple servers to ensure optimal resource utilization and prevent overload

**Benefits**:

- Improved application availability
- Enhanced performance and response times
- Scalability for handling increased traffic
- Fault tolerance and redundancy

### Load Balancing Algorithms

**Round Robin**: Requests distributed sequentially to each server

```
Request 1 → Server A
Request 2 → Server B
Request 3 → Server C
Request 4 → Server A (cycle repeats)
```

**Weighted Round Robin**: Servers assigned weights based on capacity

```
Server A (weight: 3) gets 3 requests
Server B (weight: 2) gets 2 requests
Server C (weight: 1) gets 1 request
```

**Least Connections**: Route to server with fewest active connections

**Weighted Least Connections**: Combines least connections with server weights

**IP Hash**: Route based on client IP hash (ensures session persistence)

**Least Response Time**: Route to server with fastest response time

### Types of Load Balancers

**Layer 4 Load Balancer (Transport Layer)**:

- Routes based on IP and port information
- Faster processing (no application data inspection)
- Protocol agnostic (TCP/UDP)

**Layer 7 Load Balancer (Application Layer)**:

- Routes based on application data (HTTP headers, URLs)
- Content-based routing capabilities
- SSL termination
- More processing overhead

### Load Balancer Deployment

**Hardware Load Balancers**:

- Dedicated physical devices
- High performance and reliability
- Expensive but feature-rich

**Software Load Balancers**:

- Run on standard servers
- More flexible and cost-effective
- Examples: HAProxy, NGINX, Apache HTTP Server

**Cloud Load Balancers**:

- Managed services from cloud providers
- Auto-scaling capabilities
- Examples: AWS ELB, Google Cloud Load Balancing, Azure Load Balancer

---

## 12. CDN - Content Delivery Networks {#cdns}

### What is a CDN?

**CDN (Content Delivery Network)**: Geographically distributed network of servers that deliver web content to users based on their location

**Purpose**: Reduce latency, improve performance, and enhance user experience

### How CDNs Work

```
User Request → Nearest CDN Server → Origin Server (if needed)
     ↓              ↓                      ↓
  [Client]    [Edge Server]         [Original Content]
```

**CDN Process**:

1. User requests content
2. DNS routes request to nearest CDN server
3. CDN server checks for cached content
4. If cached: serves content immediately
5. If not cached: fetches from origin server, caches, then serves

### CDN Benefits

**Performance**:

- Reduced latency through geographic proximity
- Faster content delivery
- Improved page load times

**Scalability**:

- Handles traffic spikes
- Reduces load on origin servers
- Global content distribution

**Reliability**:

- Redundancy across multiple servers
- Failover capabilities
- DDoS protection

**Cost Efficiency**:

- Reduced bandwidth costs
- Lower server infrastructure requirements

### CDN Content Types

**Static Content**:

- Images, CSS, JavaScript files
- Videos and audio files
- Documents and downloads

**Dynamic Content**:

- API responses
- Personalized content
- Real-time data

### Popular CDN Providers

- **Cloudflare**: Global network with security features
- **Amazon CloudFront**: AWS integrated CDN service
- **Akamai**: Enterprise-focused with extensive network
- **Google Cloud CDN**: Integrated with Google Cloud Platform
- **Microsoft Azure CDN**: Part of Azure ecosystem

---

## 13. WebSockets and Real-time Communication {#websockets}

### WebSockets Overview

**WebSocket**: Communication protocol providing full-duplex communication over single TCP connection

**Key Features**:

- Persistent connection
- Low latency
- Bidirectional communication
- Real-time data exchange

### WebSocket vs HTTP

| Feature       | HTTP                       | WebSocket              |
| ------------- | -------------------------- | ---------------------- |
| Connection    | Request-response           | Persistent             |
| Communication | Half-duplex                | Full-duplex            |
| Overhead      | High (headers per request) | Low (after handshake)  |
| Real-time     | Polling required           | Native support         |
| Use case      | Traditional web            | Real-time applications |

### WebSocket Handshake

```
Client Request:
GET /chat HTTP/1.1
Host: example.com
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==
Sec-WebSocket-Version: 13

Server Response:
HTTP/1.1 101 Switching Protocols
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Accept: s3pPLMBiTxaQ9kYGzzhZRbK+xOo=
```

### WebSocket Use Cases

**Real-time Applications**:

- Chat applications
- Live gaming
- Collaborative editing
- Live sports scores
- Stock price updates

**IoT and Monitoring**:

- Sensor data streaming
- System monitoring dashboards
- Real-time analytics

### Alternative Real-time Technologies

**Server-Sent Events (SSE)**:

- One-way communication (server to client)
- Built on HTTP
- Automatic reconnection
- Simpler than WebSockets

**Long Polling**:

- Extended HTTP requests
- Server holds request until data available
- Fallback for older browsers

**WebRTC**:

- Peer-to-peer communication
- Audio/video streaming
- Direct browser-to-browser connection

---

## 14. Network Performance and Optimization {#performance}

### Network Performance Metrics

**Bandwidth**: Maximum data transfer capacity

- Measured in: Mbps (Megabits per second) or Gbps (Gigabits per second)
- Determines maximum possible speed

**Latency**: Time for data to travel from source to destination

- Measured in: Milliseconds (ms)
- Lower latency = better responsiveness

**Throughput**: Actual data transfer rate achieved

- Often less than bandwidth due to network conditions
- Real-world performance metric

**Packet Loss**: Percentage of packets that don't reach destination

- Causes retransmissions and performance degradation
- Should be kept below 1% for good performance

**Jitter**: Variation in packet arrival times

- Important for real-time applications
- Measured in milliseconds

### Network Optimization Techniques

**Quality of Service (QoS)**:

- Prioritizes critical traffic
- Bandwidth allocation and traffic shaping
- Ensures important applications get resources

**Traffic Shaping**:

- Controls data flow rates
- Prevents network congestion
- Smooths traffic bursts

**Caching**:

- Stores frequently accessed data locally
- Reduces network traffic
- Improves response times

**Compression**:

- Reduces data size before transmission
- Saves bandwidth
- May increase processing overhead

### Network Monitoring

**Key Metrics to Monitor**:

- Bandwidth utilization
- Latency and response times
- Packet loss rates
- Error rates
- Device availability

**Monitoring Tools**:

- **SNMP**: Simple Network Management Protocol
- **Ping**: Basic connectivity testing
- **Traceroute**: Path analysis
- **Wireshark**: Packet analysis
- **Nagios**: Network monitoring platform

---

## 15. Common Interview Questions {#interview-questions}

### Fundamental Questions

**Q: What is the difference between OSI and TCP/IP models?**

**A**: OSI has 7 theoretical layers, TCP/IP has 4 practical layers. TCP/IP is what the Internet actually uses.

**Q: Explain the TCP three-way handshake.**

**A**:

```
Client → SYN → Server (Request connection)
Client ← SYN-ACK ← Server (Acknowledge + Request)
Client → ACK → Server (Connection established)
```

**Q: What happens when you type a URL in your browser?**

**A**:

1. DNS resolution (domain to IP)
2. TCP connection establishment
3. HTTP request sent
4. Server processes request
5. HTTP response returned
6. Browser renders content

**Q: Difference between TCP and UDP?**

**A**: TCP is reliable, connection-oriented, ordered delivery. UDP is fast, connectionless, no delivery guarantee.

### Network Layer Questions

**Q: What is subnetting and why is it used?**

**A**: Dividing large networks into smaller segments for better management, security, and performance.

**Q: Explain the difference between a router and a switch.**

**A**: Router operates at Layer 3 (IP addresses), connects different networks. Switch operates at Layer 2 (MAC addresses), connects devices within same network.

**Q: What is NAT and why is it needed?**

**A**: Network Address Translation allows multiple devices to share single public IP address, solving IPv4 address shortage.

### Application Layer Questions

**Q: Difference between HTTP and HTTPS?**

**A**: HTTPS is HTTP with SSL/TLS encryption for security. Uses port 443 vs 80 for HTTP.

**Q: What are HTTP status codes? Give examples.**

**A**:

- 2xx: Success (200 OK, 201 Created)
- 3xx: Redirection (301 Moved, 304 Not Modified)
- 4xx: Client Error (400 Bad Request, 404 Not Found)
- 5xx: Server Error (500 Internal Error, 503 Unavailable)

**Q: What is DNS and how does it work?**

**A**: Domain Name System translates domain names to IP addresses through hierarchical lookup process.

### Security Questions

**Q: What is a firewall and how does it work?**

**A**: Network security device that controls traffic based on predetermined rules, can be packet filtering or stateful inspection.

**Q: Explain VPN and its benefits.**

**A**: Virtual Private Network creates secure connection over public networks, providing confidentiality and integrity.

### Performance Questions

**Q: What is load balancing and its algorithms?**

**A**: Distributing traffic across multiple servers. Algorithms include round-robin, least connections, weighted distribution.

**Q: What is a CDN and its benefits?**

**A**: Content Delivery Network distributes content geographically to reduce latency and improve performance.

### Troubleshooting Questions

**Q: How would you troubleshoot network connectivity issues?**

**A**: Layer-by-layer approach:

1. Physical: Check cables, interfaces
2. Data Link: Verify local network connectivity
3. Network: Test IP connectivity, routing
4. Transport: Check port accessibility
5. Application: Verify service functionality

**Q: What tools would you use for network diagnosis?**

**A**: ping (connectivity), traceroute (path analysis), netstat (connections), wireshark (packet analysis), nslookup (DNS)

---

## Summary

This guide covers all essential networking concepts needed for technical interviews. Key areas to focus on:

1. **OSI/TCP-IP Models**: Understand layer responsibilities
2. **Protocols**: HTTP/HTTPS, TCP/UDP, DNS
3. **Addressing**: IP addresses, subnetting, ports
4. **Infrastructure**: Routing, switching, load balancing
5. **Security**: Firewalls, VPNs, encryption
6. **Performance**: CDNs, caching, optimization
7. **Troubleshooting**: Systematic problem-solving approach

Practice explaining these concepts clearly and be prepared to draw diagrams during interviews.
